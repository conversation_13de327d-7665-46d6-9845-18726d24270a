import { inject, Injectable } from '@angular/core';
import { QrcodeDto } from '../dto/qrcode.dto';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
class BackendServiceOptions {
  sharedKey: string = 'sharedKey';
  apiUrl: string = 'http://localhost:3000/api';
}

@Injectable({
  providedIn: 'root'
})
export class BackendService {
  readonly sharedKey: string = 'sharedKey';
  private readonly apiUrl: string;
  private http = inject(HttpClient);

  constructor(config: BackendServiceOptions) {
    this.sharedKey = config.sharedKey;
    this.apiUrl = config.apiUrl;
  }

  getQRCodeData(id: string): Observable<QrcodeDto> {
    return this.http.get<QrcodeDto>(`${this.apiUrl}/admin/qrcodes/${id}`, {
      headers: {
        'Authorization': `Bearer ${this.sharedKey}`
      }
    });
  }
}
