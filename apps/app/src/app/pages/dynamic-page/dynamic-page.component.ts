import {
  Component,
  inject,
  makeStateKey,
  PendingTasks,
  PLA<PERSON>ORM_ID,
  REQUEST_CONTEXT,
  TransferState
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

const QRCODE_KEY = makeStateKey<any>('qrcode');

@Component({
  selector: 'app-dynamic-page',
  imports: [],
  templateUrl: './dynamic-page.component.html',
  styleUrl: './dynamic-page.component.scss'
})
export class DynamicPageComponent {
  pendingTasks = inject(PendingTasks);
  platform = inject(PLATFORM_ID);
  requestContext = inject(REQUEST_CONTEXT);
  transferState = inject(TransferState);

  readonly qrcode;

  constructor() {
    this.qrcode = this.requestContext ?? null;
    if (isPlatformBrowser(this.platform)) {
      this.qrcode = this.transferState.get(QRCODE_KEY, null);
      console.log('Injected qrcode:', this.qrcode);
    } else {
      this.pendingTasks.run(async () => {
        this.transferState.set(QRCODE_KEY, this.requestContext);
      });
    }
  }
}
