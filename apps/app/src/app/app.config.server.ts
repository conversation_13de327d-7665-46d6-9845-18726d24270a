import { mergeApplicationConfig, ApplicationConfig } from '@angular/core';
import { provideServerRendering } from '@angular/platform-server';
import { provideServerRouting } from '@angular/ssr';
import { appConfig } from './app.config';
import { serverRoutes } from './app.routes.server';
import { provideHttpClient, withFetch } from '@angular/common/http';
import { BackendService } from './services/backend.service';
import { environment } from '../environments/environment';
import { provideClientHydration } from '@angular/platform-browser';

const serverConfig: ApplicationConfig = {
  providers: [
    provideServerRendering(),
    provideServerRouting(serverRoutes),
    provideHttpClient(withFetch()),
    provideClientHydration(),
    {
      provide: BackendService,
      useFactory: () => {
        return new BackendService({
          sharedKey: process.env['SHARED_KEY'] ?? 'shared',
          apiUrl: environment.apiUrl,
        });
      }
    }
  ]
};

export const config = mergeApplicationConfig(appConfig, serverConfig);
