import { computed, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, catchError, Observable, of, switchMap, take, tap, throwError } from 'rxjs';
import { UserMeDto } from '../dto/user-me.dto';
import { ApiService } from './api.service';
import { AuthLoginRequestDto } from '../dto/auth-login-request.dto';
import { AuthTokenService } from './auth-token.service';
import { CoreUrls } from '../core-urls';
import { PasswordResetRequestDto, PasswordResetChangeDto, PasswordResetResponseDto } from '../dto/password-reset.dto';

@Injectable({providedIn: 'root'})
export class AuthService {
  user = signal<UserMeDto | null>(null);
  isAuthenticated = computed(() => !!this.user());

  // Track authentication state
  private authStateSubject = new BehaviorSubject<'initial' | 'loading' | 'completed'>('initial');
  authState$ = this.authStateSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private router: Router,
    private authTokenService: AuthTokenService,
  ) {
    this.loadToken();
  }

  /**
   * Load token from localStorage and fetch current user if token exists
   */
  loadToken(): void {
    if (this.authTokenService.hasToken()) {
      this.authStateSubject.next('loading');
      this.fetchCurrentUser().subscribe({
        complete: () => this.authStateSubject.next('completed')
      });
    } else {
      this.authStateSubject.next('completed');
    }
  }

  /**
   * Login with email and password
   */
  login(loginData: AuthLoginRequestDto) {
    this.authStateSubject.next('loading');
    return this.apiService.login(loginData).pipe(
      switchMap(response => {
        this.authTokenService.setToken(response.accessToken);
        return this.fetchCurrentUser();
      }),
      tap(() => this.authStateSubject.next('completed')),
      catchError(error => {
        this.authStateSubject.next('completed');
        console.error('Login error', error);
        return throwError(() => new Error(error.error?.message || 'Login failed'));
      })
    );
  }

  /**
   * Logout the current user
   */
  logout() {
    this.authTokenService.removeToken();
    this.user.set(null);
    this.router.navigateByUrl(CoreUrls.login);
  }

  /**
   * Fetch the current user from the API
   */
  fetchCurrentUser() {
    return this.apiService.getCurrentUser().pipe(
      take(1),
      tap(user => this.user.set(user)),
      catchError(error => {
        console.error('Error fetching current user', error);
        this.authTokenService.removeToken();
        this.user.set(null);
        return of(null);
      })
    );
  }


  /**
   * Request password reset email
   * @param email User's email address
   * @returns Observable with response from the API
   */
  requestPasswordReset(email: string): Observable<PasswordResetResponseDto> {
    return this.apiService.requestPasswordReset({ email });
  }

  /**
   * Reset password with token
   * @param token Reset password token from email
   * @param password New password
   * @returns Observable with response from the API
   */
  resetPassword(token: string, password: string): Observable<PasswordResetResponseDto> {
    return this.apiService.resetPassword({ token, password });
  }
}
