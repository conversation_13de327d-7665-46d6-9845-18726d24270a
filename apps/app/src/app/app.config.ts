import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';

import { routes } from './app.routes';
import { authInterceptor } from './core/interceptors/auth.interceptor';
import { provideClientHydration, withEventReplay } from '@angular/platform-browser';
import { BackendService } from './services/backend.service';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(
      withInterceptors([authInterceptor])
    ), provideClientHydration(withEventReplay()),
    {
      provide: BackendService,
      useFactory: () => {
        console.error('BackendService loaded in frontend :(');
        return new BackendService({
          sharedKey: 'shared',
          apiUrl: 'http://localhost:3000/api',
        });
      },
    }
  ]
};
