import { EnvConfig } from './types';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AppConfig {
  database: {
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };

  auth: {
    jwt: {
      secret: string;
      expiresIn: string;
    };
    sharedKey: string;
  };

  brevo: {
    apiKey: string;
    fromName: string;
    fromEmail: string;
  };

  frontend: {
    url: string;
  }

  constructor(config: EnvConfig) {
    this.database = {
      host: config.DATABASE_HOST,
      port: config.DATABASE_PORT,
      name: config.DATABASE_NAME,
      user: config.DATABASE_USER,
      password: config.DATABASE_PASSWORD,
    };

    this.auth = {
      jwt: {
        secret: config.AUTH_JWT_SECRET,
        expiresIn: config.AUTH_JWT_EXPIRES_IN,
      },
      sharedKey: config.SHARED_KEY,
    };

    this.brevo = {
      apiKey: config.BREVO_API_KEY,
      fromName: config.BREVO_FROM_NAME,
      fromEmail: config.BREVO_FROM_EMAIL,
    };

    this.frontend = {
      url: config.FRONTEND_URL,
    };
  }
}
