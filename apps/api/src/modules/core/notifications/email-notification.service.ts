import { Injectable } from '@nestjs/common';
import { TransactionalEmailsApi, TransactionalEmailsApiApiKeys } from '@getbrevo/brevo';
import { AppConfig } from '../../../config/app-config';

export interface EmailInfo {
  name?: string;
  email: string;
}

export interface ConfirmEmailParams extends EmailInfo {
  displayName: string;
  confirmUrl: string;
}

export interface PasswordResetParams extends EmailInfo {
  displayName: string;
  resetUrl: string;
}

const TemplateIDs = {
  confirmEmail: 7,
  passwordReset: 8, // Zakładam ID szablonu dla resetowania hasła
}

@Injectable()
export class EmailNotificationService {
  private api = new TransactionalEmailsApi();

  constructor(private readonly config: AppConfig) {
    this.api.setApiKey(TransactionalEmailsApiApiKeys.apiKey, this.config.brevo.apiKey);
  }

  async send(to: EmailInfo, templateId: number, params: any) {
    await this.api.sendTransacEmail({
      to: [to],
      templateId: templateId,
      params: params as any,
    });
  }

  async sendConfirmEmail(params: ConfirmEmailParams) {
    await this.send({
      name: params.name,
      email: params.email,
    }, TemplateIDs.confirmEmail, params);
  }

  async sendPasswordReset(params: PasswordResetParams) {
    await this.send({
      name: params.name,
      email: params.email,
    }, TemplateIDs.passwordReset, params);
  }
}
