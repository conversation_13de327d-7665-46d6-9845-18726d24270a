import { Body, Controller, Get, NotFoundException, Post, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { AuthService } from './auth.service';
import { UserEntity } from '../users/entities/user.entity';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { mapUserDtoFromEntity } from '../users/map-user-dto-from-entity';
import { AuthLoginRequestDto } from './dto/auth-login-request.dto';
import { Public } from './decorators/public';
import { ApiResponse } from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import { UserMeDto } from './dto/user-me.dto';
import { OrganizationsService } from '../organizations/organizations.service';
import { CurrentOrganizationId } from './decorators/current-organization-id';
import { SwitchOrganizationRequestDto } from '../organizations/dto/switch-organization-request.dto';
import { CurrentUser } from './decorators/current-user';
import { SwitchOrganizationResponseDto } from '../organizations/dto/switch-organization-response.dto';
import { OrganizationDto } from '../organizations/dto/organization.dto';
import { mapOrganizationDtoFromEntity } from '../organizations/utils/map-organization-dto-from-entity';
import { PasswordResetRequestDto } from './dto/password-reset-request.dto';
import { PasswordResetChangeDto } from './dto/password-reset-change.dto';
import { PasswordResetResponseDto } from './dto/password-reset-response.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly organizationsService: OrganizationsService,
  ) {}

  @Public()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiResponse({ type: AuthResponseDto })
  async login(@Req() req: Request, @Body() body: AuthLoginRequestDto): Promise<AuthResponseDto> {
    console.log('LOGIN');
    const user = req.user as UserEntity;
    return this.authService.login(user, body.organizationId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  @ApiResponse({ type: UserMeDto })
  async me(@Req() req: Request, @CurrentOrganizationId() organizationId: string | null): Promise<UserMeDto> {
    const user = req.user as UserEntity;
    return {
      ...mapUserDtoFromEntity(user),
      currentOrganization: await this.getOrganization(organizationId, user.id),
    };
  }

  async getOrganization(organizationId: string | null, userId: string): Promise<UserMeDto['currentOrganization']> {
    if (!organizationId) {
      return null;
    }

    const organization = await this.organizationsService.findOne(organizationId);
    if (!organization) {
      return null;
    }

    // Find the user's role in this organization
    const member = organization.members.find((m) => m.userId === userId);

    if (!member) {
      return null;
    }

    return {
      id: organization.id,
      name: organization.name,
      role: member.role,
    };
  }

  @Post('organization')
  async switchOrganization(
    @Body() body: SwitchOrganizationRequestDto,
    @CurrentUser() user: UserEntity,
  ): Promise<SwitchOrganizationResponseDto> {
    const organization = await this.organizationsService.findOne(body.organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if user is a member of the organization
    if (!organization.members.find((member) => member.user.id === user.id)) {
      throw new NotFoundException('Organization not found');
    }

    // Generate new token with new organization ID
    return this.authService.login(user, body.organizationId);
  }

  @Get('organization')
  async getCurrentWorkspace(
    @CurrentOrganizationId() organizationId: string,
    @CurrentUser() user: UserEntity,
  ): Promise<OrganizationDto> {
    const organization = await this.organizationsService.findOne(organizationId);
    return mapOrganizationDtoFromEntity(organization);
  }

  @Public()
  @Post('password/send')
  @ApiResponse({ type: PasswordResetResponseDto })
  async sendPasswordReset(@Body() body: PasswordResetRequestDto): Promise<PasswordResetResponseDto> {
    const success = await this.authService.sendPasswordResetLink(body.email);
    return {
      success,
      message: success
        ? 'Email with reset instructions has been sent.'
        : 'If the email exists in our system, reset instructions will be sent.',
    };
  }

  @Public()
  @Post('password/reset')
  @ApiResponse({ type: PasswordResetResponseDto })
  async resetPassword(@Body() body: PasswordResetChangeDto): Promise<PasswordResetResponseDto> {
    const success = await this.authService.resetPassword(body.token, body.password);
    return {
      success,
      message: success
        ? 'Password has been successfully reset.'
        : 'Failed to reset password. The token may be invalid or expired.',
    };
  }
}
