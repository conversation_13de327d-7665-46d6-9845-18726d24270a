import { Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserEntity } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
import { UsersConfigService } from '../users/users-config.service';
import { compare, hash } from 'bcrypt';
import { OrganizationsService } from '../organizations/organizations.service';
import { EmailNotificationService } from '../notifications/email-notification.service';
import { AppConfig } from '../../../config/app-config';

// Payload dla tokenów JWT służących do resetowania hasła
interface PasswordResetTokenPayload {
  sub: string;      // ID użytkownika
  email: string;    // Email użytkownika
  type: 'password-reset'; // Typ tokenu
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly userService: UsersService,
    private readonly jwtService: JwtService,
    private readonly usersConfigService: UsersConfigService,
    private readonly organizationsService: OrganizationsService,
    private readonly emailService: EmailNotificationService,
    private readonly config: AppConfig,
  ) {}

  async withPassword(email: string, password: string): Promise<UserEntity | null> {
    const user = await this.userService.getByEmail(email);
    if (!user || user.passwordDigest === null || user.blocked) {
      return null;
    }

    if (await compare(password, user.passwordDigest)) {
      return user;
    }

    return null;
  }

  /**
   * Create a new JWT token for given user.
   * @param user
   * @param organizationId
   */
  async login(user: UserEntity, organizationId?: string): Promise<{ accessToken: string } | null> {
    if (!user || user.blocked) {
      return null;
    }

    let finalOrganizationId = organizationId;

    if (!finalOrganizationId) {
      // If organizationId is not provided, try to get the last used organization from user config
      finalOrganizationId = await this.usersConfigService.getSingle(user.id, 'lastOrganizationId', null);
    } else {
      // If organizationId is provided, update the last used organization in user config
      await this.usersConfigService.setConfig(user.id, { lastOrganizationId: organizationId });
    }

    if (finalOrganizationId) {
      const organization = await this.organizationsService.findOne(finalOrganizationId);
      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      // Check if user is a member of the organization
      if (!organization.members.find((member) => member.user.id === user.id)) {
        throw new NotFoundException('Organization not found');
      }
    }

    const payload = {
      sub: user.id,
      oid: finalOrganizationId // Add workspace ID to payload
    };

    return {
      accessToken: this.jwtService.sign(payload),
    };
  }

  async withId(id: string): Promise<UserEntity | null> {
    const user = await this.userService.getById(id);
    if (!user) {
      this.logger.error(`User with ID ${id} not found`);
      return null;
    }

    if (user.blocked) {
      this.logger.error(`User with ID ${id} is blocked`);
      return null;
    }

    return user;
  }

  /**
   * Generate a password reset token and send an email with reset link
   * @param email User email address
   * @returns true if email was sent, false if user not found
   */
  async sendPasswordResetLink(email: string): Promise<boolean> {
    const user = await this.userService.getByEmail(email);
    if (!user) {
      // For security reasons, we don't want to reveal that the email doesn't exist
      return false;
    }

    // Create a JWT token for password reset
    const payload: PasswordResetTokenPayload = {
      sub: user.id,
      email: user.email,
      type: 'password-reset',
    };

    const token = this.jwtService.sign(payload, {
      secret: this.config.auth.jwt.secret,
      expiresIn: '1h', // Token ważny przez 1 godzinę
    });

    // Generate reset URL with the token
    const resetUrl = `${this.config.frontend.url}/reset-password?token=${token}`;

    // Send email with reset link
    try {
      await this.emailService.sendPasswordReset({
        email: user.email,
        displayName: user.displayName,
        resetUrl,
      });
      return true;
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${user.email}`, error);
      return false;
    }
  }

  /**
   * Reset user's password using a valid token
   * @param token The JWT reset token
   * @param newPassword New password to set
   * @returns true if password was successfully reset
   */
  async resetPassword(token: string, newPassword: string): Promise<boolean> {
    try {
      // Verify and decode the token
      const payload = this.jwtService.verify(token, {
        secret: this.config.auth.jwt.secret,
      }) as PasswordResetTokenPayload;

      // Verify token type
      if (payload.type !== 'password-reset') {
        throw new UnauthorizedException('Invalid token type');
      }

      const userId = payload.sub;

      // Get user
      const user = await this.userService.getById(userId);
      if (!user || user.email !== payload.email) {
        throw new UnauthorizedException('User not found or email mismatch');
      }

      // Hash the new password
      const passwordDigest = await hash(newPassword, 10);

      // Update user's password
      await this.userService.updatePassword(userId, passwordDigest);

      return true;
    } catch (error) {
      this.logger.error('Password reset failed', error);
      return false;
    }
  }
}
