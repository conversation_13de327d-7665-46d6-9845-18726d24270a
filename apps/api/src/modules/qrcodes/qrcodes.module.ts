import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QrcodeEntity } from './entities/qrcode.entity';
import { QrcodesService } from './qrcodes.service';
import { QrcodesController } from './qrcodes.controller';
import { FoldersModule } from '../core/folders/folders.module';
import { ConfigModule } from '../../config/config.module';

@Module({
  imports: [ConfigModule, TypeOrmModule.forFeature([QrcodeEntity]), FoldersModule],
  controllers: [QrcodesController],
  providers: [QrcodesService],
  exports: [QrcodesService],
})
export class QrcodesModule {}
